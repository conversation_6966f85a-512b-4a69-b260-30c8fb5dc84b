package game

import (
	"casrv/server/common/config"
	"casrv/server/common/pb"
	ut "casrv/utils"
	"math"
)

// 一个英雄
type Hero struct {
	UID        string    `json:"uid"`
	ExtraAttrs [][]int32 `json:"extraAttrs"` //额外属性 {type, id, value} type: 0.基础属性 1.效果
	Id         int32     `json:"id"`
	Cost       int32     `json:"cost"`     //费用
	Quality    int8      `json:"quality"`  //品质 1.普通 2.优秀 3.卓越 4.史诗 5.传说
	AreaType   int8      `json:"areaType"` //所在区域
	Index      int8      `json:"index"`    //位置
}

func (this *Hero) GetUID() string           { return this.UID }
func (this *Hero) GetExtraAttrs() [][]int32 { return this.ExtraAttrs }
func (this *Hero) GetId() int32             { return this.Id }
func (this *Hero) GetCost() int32           { return this.Cost }
func (this *Hero) GetQuality() int8         { return this.Quality }
func (this *Hero) GetAreaType() int8        { return this.AreaType }
func (this *Hero) GetIndex() int8           { return this.Index }

func (this *Hero) ToPb() *pb.Hero {
	return &pb.Hero{
		Uid:        this.UID,
		Id:         this.Id,
		Quality:    int32(this.Quality),
		ExtraAttrs: CloneAttrsToPb(this.ExtraAttrs),
		Cost:       this.Cost,
		AreaType:   int32(this.AreaType),
		Index:      int32(this.Index),
	}
}

func NewHeroByJson(id int32, quality int8, areaType, index int8) *Hero {
	hero := &Hero{
		UID:        ut.ID(),
		Id:         id,
		Quality:    quality,
		Cost:       int32(math.Pow(float64(HERO_COST_ONE_QUALITY), float64(quality))),
		AreaType:   areaType,
		Index:      index,
		ExtraAttrs: [][]int32{},
	}
	return hero
}

// // 初始化属性
// func (this *Hero) InitAttrs() {
// 	attrJson := config.GetJsonData("heroAttr", this.Id*100+int32(this.Quality))
// 	if attrJson == nil {
// 		log.Error("Hero InitAttrs error! id: %v, quality: %v", this.Id, this.Quality)
// 		return
// 	}
// 	this.Attrs = [][]int32{}
// 	// 效果
// 	if effects := ut.String(attrJson["effects"]); effects != "" {
// 		effectList := strings.SplitSeq(effects, "|")
// 		for m := range effectList {
// 			arr := ut.StringToInt32s(m, ",")
// 			this.Attrs = append(this.Attrs, append([]int32{HERO_ATTR_TYPE_EFFECT}, arr...))
// 		}
// 	}
// }

// 是否满品质
func (this *Hero) IsMaxQuality() bool {
	nextAttrId := this.Id*100 + int32(this.Quality+1)
	return config.GetJsonData("heroAttr", nextAttrId) == nil
}

// 升级品质
func (this *Hero) UpQuality() {
	if !this.IsMaxQuality() {
		this.Quality += 1
	}
}
