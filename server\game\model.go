package game

import (
	"casrv/server/common/config"
	"casrv/server/common/pb"
	rds "casrv/server/common/redis"
	"casrv/server/game/battle"
	ut "casrv/utils"
	"casrv/utils/array"
	"encoding/json"
	"sort"

	"github.com/huyangv/vmqant/log"
)

// 游戏数据
type GameData struct {
	Player     *PlayerInfo    `json:"player"`     //自己的信息
	Encounter  *EncounterInfo `json:"encounter"`  //遭遇信息
	HeroDeck   []int32        `json:"heroDeck"`   //卡组
	MapPaths   []int32        `json:"mapPaths"`   //走过的路径
	CreateTime int64          `json:"createTime"` //创建时间
}

func (this *GameData) ToPb() *pb.GameData {
	return &pb.GameData{
		Player:    this.Player.ToPb(),
		Encounter: this.Encounter.ToPb(),
		MapPaths:  array.Clone(this.MapPaths),
	}
}

// 获取游戏数据
func GetGameData(uid string) *GameData {
	jsonStr, err := rds.RdsHGet(rds.RDS_GAME_DATA_KEY+uid, "data")
	if err != nil || jsonStr == "" {
		return nil
	}
	// 将JSON字符串反序列化为GameData对象
	gameData := new(GameData)
	if err := json.Unmarshal([]byte(jsonStr), gameData); err != nil {
		log.Error("GetGameData unmarshal error: %v", err)
		return nil
	} else if gameData.Encounter == nil {
		gameData.Encounter = new(EncounterInfo)
	}
	return gameData
}

// 保存游戏数据到redis
func SaveGameData(uid string, data *GameData) error {
	// 将GameData序列化为JSON字符串
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		log.Error("SaveGameData marshal error: %v", err)
		return err
	}
	// 保存到redis hash中
	return rds.RdsHSet(rds.RDS_GAME_DATA_KEY+uid, "data", string(jsonBytes))
}

// 创建游戏信息
func CreateGame(uid string, nickname string, roleId int32) *GameData {
	data := &GameData{CreateTime: ut.Now(), MapPaths: []int32{}, Encounter: new(EncounterInfo)}
	// 自己的数据
	data.Player = NewPlayer(uid, nickname, roleId)
	// 初始化开始的遭遇
	data.ResetEncounter(MAP_NODE_TYPE_TAVERN)
	// 添加路径 默认添加第一个路径
	data.MapPaths = append(data.MapPaths, 0)
	// 小时增加
	data.Player.Hour += 1
	// 保存到redis
	SaveGameData(uid, data)
	return data
}

// 进行下一天
func (this *GameData) NextDay() {
	this.CleanEncounterData() //清空遭遇
	this.MapPaths = []int32{} //清空路径
	player := this.Player
	player.Day += 1
	player.Hour = 0 //重置小时数
	player.Gold += player.Earnings
}

// 清理遭遇信息
func (this *GameData) CleanEncounterData() {
	this.Encounter.CleanData()
}

// 重置遭遇
func (this *GameData) ResetEncounter(tp int32) battle.IPlayerInfo {
	this.CleanEncounterData()
	this.Encounter.Type = tp
	switch tp {
	case MAP_NODE_TYPE_ENEMY_PLAYER:
		// this.Encounter = CreatePlayer()
		this.Encounter.Type = MAP_NODE_TYPE_ENEMY_BATTLE
		this.Encounter.Enemy = this.CreateEnemy()
		return this.Encounter.Enemy
	case MAP_NODE_TYPE_ENEMY_BATTLE:
		this.Encounter.Enemy = this.CreateEnemy()
		return this.Encounter.Enemy
	case MAP_NODE_TYPE_TAVERN:
		this.Encounter.Tavern = this.CreateTavern(3)
	}
	return nil
}

// 创建酒馆信息
func (this *GameData) CreateTavern(count int) *TavernInfo {
	player := this.Player
	day := player.Day
	// 所有英雄map
	allHerosMap := player.GetAllHerosMap()
	// 获取可刷出的品质范围
	minQuality, maxQuality := DIAMOND, DIAMOND
	if minQualityArr := array.Find(MIN_QUALITY_BY_DAY, func(m []int32) bool { return day < m[0] }); minQualityArr != nil {
		minQuality = int8(minQualityArr[1])
		maxQuality = int8(minQualityArr[2])
	}
	// 获取配置列表
	items, totalWeight := []map[string]any{}, 0
	datas := config.GetJson("heroBase").Datas
	for _, m := range datas {
		if heros := allHerosMap[ut.Int32(m["id"])]; array.Some(heros, func(m *Hero) bool { return m.IsMaxQuality() || m.Quality >= maxQuality }) {
			continue //已经满级了
		} else if weight := ut.Int(m["weight"]); weight > 0 {
			items = append(items, m)
			totalWeight += weight //总权重信息
		}
	}
	// 随机3个英雄
	indexs := ut.RandomMultipleIndexByWeightHasTotal(items, totalWeight, 3)
	heros := array.Map(indexs, func(index int, i int) *Hero {
		json := items[index]
		quality := ut.Int8(json["quality"])
		// 如果已经有了 至少是最低品质
		if heros := allHerosMap[ut.Int32(json["id"])]; len(heros) > 0 {
			//从小到大排序
			sort.Slice(heros, func(i, j int) bool { return heros[i].Quality < heros[j].Quality })
			quality = heros[0].Quality
		}
		quality = ut.MaxInt8(quality, minQuality)
		return NewHeroByJson(ut.Int32(json["id"]), quality, AREA_TYPE_SHOP, int8(i))
	})
	return &TavernInfo{Heros: heros}
}

// 创建敌人信息
func (this *GameData) CreateEnemy() *EnemyInfo {
	datas, totalWeight := config.GetJson("heroBase").Datas, 0
	for _, m := range datas {
		totalWeight += ut.Int(m["weight"]) //总权重信息
	}
	indexs := ut.RandomMultipleIndexByWeightHasTotal(datas, totalWeight, ut.Random(2, 9))
	battleArea := Area{}
	for i, index := range indexs {
		json := datas[index]
		quality := ut.Int8(json["quality"])
		battleArea[int8(i)] = NewHeroByJson(ut.Int32(json["id"]), int8(ut.Random(int(ut.MaxInt8(quality, BRONZE)), int(DIAMOND))), AREA_TYPE_ENEMY, int8(i))
	}
	return &EnemyInfo{BattleArea: battleArea}
}
