package game

import (
	"casrv/server/common/pb"
	"casrv/server/game/battle"
	"casrv/utils/array"
)

// 拷贝属性
func CloneAttrs(attrs [][]int32) [][]int32 {
	ret := [][]int32{}
	for i, l := 0, len(attrs); i < l; i++ {
		ret = append(ret, array.Clone(attrs[i]))
	}
	return ret
}

// 拷贝属性到pb
func CloneAttrsToPb(attrs [][]int32) []*pb.Int32ArrayInfo {
	arr := []*pb.Int32ArrayInfo{}
	for _, attr := range attrs {
		attrInfo := &pb.Int32ArrayInfo{}
		for _, val := range attr {
			attrInfo.Arr = append(attrInfo.Arr, val)
		}
		arr = append(arr, attrInfo)
	}
	return arr
}

func ToAreaPb(area Area) map[int32]*pb.Hero {
	ret := map[int32]*pb.Hero{}
	for k, v := range area {
		ret[int32(k)] = v.ToPb()
	}
	return ret
}

func ToBattleArea(area Area) battle.Area {
	battleAreas := make(battle.Area)
	for k, v := range area {
		battleAreas[k] = v
	}
	return battleAreas
}
