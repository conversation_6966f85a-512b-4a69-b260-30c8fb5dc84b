import { _decorator, Node, Prefab, EventTouch, Vec3, Vec2, v2, v3, Size, size, Rect, rect, UITransform } from "cc";
import HeroCmpt from "./HeroCmpt";
import GameModel from "../../model/game/GameModel";
import EventType from "../../common/event/EventType";
import AnimPlayCmpt from "./AnimPlayCmpt";
import RoleCmpt from "./RoleCmpt";
import RoleObj from "../../model/game/RoleObj";
import { vHelper } from "../../common/helper/ViewHelper";
import HeroObj from "../../model/game/HeroObj";
import PlayerModel from "../../model/game/PlayerModel";
import { AreaType, MapNodeType } from "../../common/constant/Enums";
import { gHelper } from "../../common/helper/GameHelper";
const { ccclass } = _decorator;

@ccclass
export default class GameWindCtrl extends mc.BaseWindCtrl {

    //@autocode property begin
    private chessboardNode_: Node = null // path://chessboard_n
    private itemsNode_: Node = null // path://items_n
    private flutterNode_: Node = null // path://flutter_n
    //@end

    private animPlay: AnimPlayCmpt = null
    private encounterArea: Node = null
    private root: Node = null
    private recycleRoot: Node = null
    private areaPositionMap: { [key: number]: { [key: number]: Vec3 } } = {}
    private recycleRect: Rect = rect()

    private model: GameModel = null
    private player: PlayerModel = null

    private heros: HeroCmpt[] = []
    private heroMap: { [key: string]: HeroCmpt } = {}
    private myRole: RoleCmpt = null

    private _temp_vec2_1: Vec2 = v2()

    public listenEventMaps() {
        return [
            { [EventType.UPDATE_GAME_INFO]: this.onUpdateGameInfo, enter: true },
            { [EventType.UPDATE_BATTLE_AREA]: this.onUpdateBattleArea, enter: true },
            { [EventType.UPDATE_ENCOUNTER_AREA]: this.onUpdateEncounterArea, enter: true },
            { [EventType.DRAG_HERO_BEGIN]: this.onDragHeroBegin, enter: true },
            { [EventType.DRAG_HERO_MOVE]: this.onDragHeroMove, enter: true },
            { [EventType.DRAG_HERO_END]: this.onDragHeroEnd, enter: true },
            { [EventType.REMOVE_HERO]: this.onRemoveHero, enter: true },
            { [EventType.PLAY_FLUTTER_HP]: this.onPlayFlutterHp, enter: true },
        ]
    }

    public async onCreate() {
        this.encounterArea = this.chessboardNode_.FindChild('encounter')
        this.root = this.chessboardNode_.FindChild('root')
        this.recycleRoot = this.chessboardNode_.FindChild('recycle/root')
        this.animPlay = this.node.addComponent(AnimPlayCmpt)
        this.areaPositionMap = {}
        // 自己的位置
        this.chessboardNode_.FindChild('area').children.forEach(m => {
            const area = this.areaPositionMap[m.name] = {}
            m.FindChild('pos').children.forEach(n => area[n.name] = ut.convertToNodeAR(n, this.root).clone().toVec3())
        })
        // 商店的位置
        let area = this.areaPositionMap[AreaType.TAVERN] = {}
        this.encounterArea.FindChild(MapNodeType.TAVERN + '/pos').children.forEach(n => area[n.name] = ut.convertToNodeAR(n, this.root).clone().toVec3())
        // 敌人的位置
        area = this.areaPositionMap[AreaType.ENEMY] = {}
        this.encounterArea.FindChild(MapNodeType.ENEMY_BATTLE + '/pos').children.forEach(n => area[n.name] = ut.convertToNodeAR(n, this.root).clone().toVec3())
        // 直接获取 recycleRoot 在 root 坐标系中的矩形区域
        this.recycleRect = this.recycleRoot.transform.getBoundingBoxTo(this.root.getWorldMatrix())
        this.model = this.getModel('game')
        this.player = this.getModel('player')
        this.myRole = await this.createRole(new RoleObj().init(110001), 0)
        await this.animPlay.init(this.key)
    }

    public onEnter(data: any) {
        this.model.enter()
        this.myRole.playAnimation('idle')
        this.recycleRoot.Child('bg').opacity = 10
        this.initHeroArea()
        this.initEncounterArea()
    }

    public onClean() {
        this.model.leave()
        this.animPlay.clean()
        this.animPlay = null
        assetsMgr.releaseTempResByTag(this.key)
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://chessboard_n/goon_be
    onClickGoon(event: EventTouch, data: string) {
        this.myRole.playAnimation('attack', () => this.myRole.playAnimation('idle'))
        vHelper.showPnl('game/Map')
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // 刷新游戏信息
    private onUpdateGameInfo() {
        this.initHeroArea()
        this.initEncounterArea()
    }

    // 刷新战斗区域
    private onUpdateBattleArea() {
        this.initHeroArea()
    }

    // 刷新遭遇区域
    private onUpdateEncounterArea() {
        this.initEncounterArea()
    }

    // 拖动英雄开始
    private onDragHeroBegin(hero: HeroCmpt) {
        if (hero.areaType === AreaType.BATTLE || hero.areaType === AreaType.PREPARE) {
            this.recycleRoot.Child('bg').opacity = 100
        }
    }

    // 拖动英雄移动
    private onDragHeroMove(hero: HeroCmpt) {
        if (hero.areaType === AreaType.BATTLE || hero.areaType === AreaType.PREPARE) {
            this.recycleRoot.Child('bg').opacity = this.checkInRecycleRange(hero.getTempPosition()) ? 255 : 100
        }
    }

    // 拖动英雄结束
    private onDragHeroEnd(hero: HeroCmpt) {
        this.recycleRoot.Child('bg').opacity = 10
        const heroPosition = hero.getTempPosition()
        const type = hero.areaType
        if (type === AreaType.BATTLE || type === AreaType.PREPARE) { //英雄区域
            if (this.checkInRecycleRange(heroPosition)) {
                return this.sellHero(hero) //卖出
            }
            const [areaType, index] = this.findMinDisAreaIndex(heroPosition)
            if (areaType === hero.areaType && index === hero.index) {
                return hero.restorePosition()
            }
            this.moveHero(hero, areaType, index)
        } else if (type === AreaType.TAVERN) { //酒馆区域
            if (hero.node.x >= 0) { //没有超过 中线就不算购买
                return hero.restorePosition()
            }
            const [areaType, index] = this.findMinDisAreaIndex(heroPosition)
            if (areaType === hero.areaType && index === hero.index) {
                return hero.restorePosition()
            }
            this.buyHero(hero, areaType, index)
        }
    }

    // 删除英雄
    private onRemoveHero(uid: string, release: boolean) {
        this.cleanHero(this.heros.remove('uid', uid), release)
    }

    // 播放飘字
    private onPlayFlutterHp(data: any) {
        const node = this.heros.find(m => m.uid === data.uid)?.node
        if (node) {
            this.animPlay.playFlutter(data, this.flutterNode_, ut.convertToNodeAR(node, this.flutterNode_))
        }
    }
    // ----------------------------------------- custom function ----------------------------------------------------

    update(dt: number) {
        this.model?.update(dt)
    }

    // 创建一个角色
    private async createRole(data: RoleObj, index: number) {
        if (!this.isValid || !data) {
            return null
        }
        const pfb = await assetsMgr.loadTempRes(data.getPrefabUrl(), Prefab, this.key)
        if (!pfb || !this.isValid) {
            return null
        }
        const pos = this.chessboardNode_.Child('role_pos/' + index).getPosition()
        const role = await mc.instantiate(pfb, this.itemsNode_).getComponent(RoleCmpt).init(data, pos, this.key)
        return role
    }

    // 创建一个英雄
    private async createHero(data: HeroObj) {
        if (!this.isValid || !data) {
            return
        }
        const pos = this.areaPositionMap[data.areaType][data.index]
        let hero = this.heros.find(m => m.uid === data.uid)
        if (!hero) {
        } else if (data.isDie()) {
            this.heros.remove('uid', data.uid)
            this.cleanHero(hero)
            return
        } else {
            return hero.resync(data, pos)
        }
        const node = await nodePoolMgr.get('hero/HERO', this.key)
        if (!node || !node.isValid || !this.isValid) {
            return
        } else if (this.heroMap[data.uid]) {
            return //防止多次创建
        } else if (!data || data.isDie()) {
            return
        }
        node.parent = this.root
        hero = await node.Component(HeroCmpt).init(data, pos, this.key)
        if (this.isValid) {
            this.heros.push(hero)
            this.heroMap[data.uid] = hero
        }
    }

    private cleanHero(hero: HeroCmpt, release?: boolean) {
        if (hero) {
            delete this.heroMap[hero.uid]
            hero.clean(release)
        }
    }

    // 运行战斗
    private async runBattle() {
        // await Promise.all(this.model.getEnemyAnimals().map(m => this.createHero(m, 'enemy')))
        // this.model.battleBegin()
    }

    // 清理区域所有英雄
    private cleanAreaAllHero(heros: HeroObj[], isMy: boolean) {
        const uidMap = {}
        heros.forEach(m => uidMap[m.getAbsUid()] = true)
        for (let i = this.heros.length - 1; i >= 0; i--) {
            const m = this.heros[i]
            if (isMy && !m.isMyArea()) {
                continue
            } else if (!isMy && m.isMyArea()) {
                continue
            } else if (!uidMap[m.getAbsUid()] || !m.data || m.data.isDie()) {
                this.cleanHero(this.heros.splice(i, 1)[0])
            }
        }
        return heros
    }

    // 初始化英雄区域
    private initHeroArea() {
        this.cleanAreaAllHero(this.player.getHeros(), true).forEach(m => this.createHero(m))
    }

    // 初始化遭遇区域
    private initEncounterArea() {
        let cleanHero = true
        const encounter = this.model.getEncounter(), type = encounter.type, data = encounter.getData()
        const node = this.encounterArea.Swih(type)[0]
        if (type === MapNodeType.TAVERN) { //酒馆
            if (data?.heros?.length) {
                cleanHero = false
                this.cleanAreaAllHero(data.heros.map(m => new HeroObj().init(m)), false).forEach(m => this.createHero(m))
            }
        } else if (type === MapNodeType.ENEMY_BATTLE) { //野怪
            const battleAreas = gHelper.initAreaDataToArray(data.battleAreas || {}) //战斗区域
            if (battleAreas.length) {
                cleanHero = false
                this.cleanAreaAllHero(battleAreas, false).forEach(m => this.createHero(m))
            }
        }
        // 如果没有清理这里就清理
        if (cleanHero) {
            this.cleanAreaAllHero([], false)
        }
    }

    // 检测是否在回收区域
    private checkInRecycleRange(pos: Vec3) {
        return this.recycleRect.contains(v2(pos.x, pos.y))
    }

    // 找出英雄最近的区域位置
    private findMinDisAreaIndex(pos: Vec3) {
        let temp = v3(), minMag = ut.MAX_VALUE, areaType = -1, index = -1
        for (let _areaType in this.areaPositionMap) {
            const area = this.areaPositionMap[_areaType]
            for (let _index in area) {
                const p = temp.set(area[_index])
                const mag = p.subtract(pos).length()
                if (mag < minMag) {
                    areaType = Number(_areaType)
                    index = Number(_index)
                    minMag = mag
                }
            }
        }
        return [areaType, index]
    }

    // 购买英雄
    private async buyHero(hero: HeroCmpt, areaType: number, index: number) {
        const err = await this.model.buyHero(hero.uid, areaType, index)
        if (err) {
            hero.restorePosition()
            return vHelper.showAlert(err)
        }
        this.initHeroArea()
        this.initEncounterArea()
    }

    // 出售英雄
    private async sellHero(hero: HeroCmpt) {
        const err = await this.model.sellHero(hero.uid)
        if (err) {
            hero.restorePosition()
            return vHelper.showAlert(err)
        }
        this.initHeroArea()
    }

    // 移动英雄
    private async moveHero(hero: HeroCmpt, areaType: number, index: number) {
        const err = await this.model.moveHero(hero.uid, areaType, index)
        if (err) {
            hero.restorePosition()
            return vHelper.showAlert(err)
        }
        this.initHeroArea()
    }
}
