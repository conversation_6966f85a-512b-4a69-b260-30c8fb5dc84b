import { sys } from "cc"
import GameModel from "../../model/game/GameModel"
import { localConfig } from "../LocalConfig"
import ac from "db://assets/scene/ac"
import UserModel from "../../model/common/UserModel"
import NetworkModel from "../../model/common/NetworkModel"
import PlayerModel from "../../model/game/PlayerModel"
import HeroObj from "../../model/game/HeroObj"

/**
 * 游戏逻辑帮助类
 */
class GameHelper {

    public get net() { return NetworkModel.ins<NetworkModel>() }
    public get user() { return UserModel.ins<UserModel>() }
    public get player() { return PlayerModel.ins<PlayerModel>() }
    public get game() { return GameModel.ins<GameModel>() }

    public clickTouchId: number = -1 //公共的触摸id

    public getAppType() { return ac.getAppType() }
    public isInland() { return ac.getAppType() === 'inland' }
    public isGLobal() { return ac.getAppType() === 'global' }
    public getUid() { return this.user.getUid() }
    public getNickname() { return this.user.getNickname() }

    // 获取服务器地址
    public getServerUrl() {
        // if (!this.isRelease) {
        //     return localConfig.server_test
        // } else if (this.isGLobal()) {
        //     return localConfig.servers[this.getServerArea()] || localConfig.servers.hk
        // } else if (!ut.isWechatGame() || wxHelper.isRelease()) {
        //     return localConfig.servers.china
        // }
        // return localConfig.server_test
        return localConfig.server_test
    }

    // 获取http服务器url
    public getHttpServerUrl(): string {
        // if (!this.isRelease) {
        //     return localConfig.httpServerUrl.test
        // }
        // return localConfig.httpServerUrl[this.getServerArea()] || localConfig.httpServerUrl.hk
        return localConfig.httpServerUrl.test
    }

    // 获取运行平台
    public getRunPlatform() {
        if (ut.isMobile()) {
            return ut.isAndroid() ? 'android' : 'ios'
        } else if (ut.isMiniGame()) {
            return typeof qq != 'undefined' ? 'qq' : 'wx'
        }
        return 'none'
    }

    // 获取商店平台
    public getShopPlatform() {
        const isGLobal = this.isGLobal()
        if (ut.isIos()) {
            return isGLobal ? 'ios_global' : 'ios_inland'
        } else if (ut.isAndroid()) {
            return isGLobal ? 'google' : 'taptap'
        } else if (sys.isBrowser) {
            return 'web'
        }
        return sys.os + '_' + sys.platform
    }

    // 初始化区域数据
    public initAreaData(data: any) {
        const areas: { [key: number]: HeroObj } = {}
        for (let key in data) {
            areas[key] = new HeroObj().init(data[key]) //初始化武将数据
        }
        return areas
    }
    public initAreaDataToArray(data: any) {
        const areas: HeroObj[] = []
        for (let key in data) {
            areas.push(new HeroObj().init(data[key])) //初始化武将数据
        }
        return areas
    }
}

export const gHelper = new GameHelper()
if (sys.isBrowser) {
    window['gHelper'] = gHelper
}