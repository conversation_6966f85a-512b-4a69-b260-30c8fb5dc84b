import { g<PERSON><PERSON><PERSON> } from "../../common/helper/GameHelper"
import NetworkModel from "../common/NetworkModel"
import HeroObj from "./HeroObj"

/**
 * 玩家信息
 */
@mc.addmodel('player')
export default class PlayerModel extends mc.BaseModel {

    private net: NetworkModel = null

    private battleAreas: { [key: number]: HeroObj } = {} //战斗区域
    private prepareAreas: { [key: number]: HeroObj } = {} //备战区域
    private roleId: number = 0 //角色id
    private day: number = 0 //天数
    private hour: number = 0 //小时
    private hp: number = 0 //血量
    private winCount: number = 0 //胜利次数
    private gold: number = 0 //金币
    private earnings: number = 0 //收益

    public onCreate() {
        this.net = this.getModel('net')
    }

    public initBaseData(data: any) {
        this.day = data.day || 0 //天数
        this.hp = data.hp
        this.winCount = data.winCount || 0 //胜利次数
    }

    public init(data: any) {
        this.roleId = data.roleId || 0 //角色id
        this.day = data.day || 0 //天数
        this.hour = data.hour || 0 //小时
        this.hp = data.hp || 0 //血量
        this.winCount = data.winCount || 0 //胜利次数
        this.gold = data.gold || 0 //金币
        this.earnings = data.earnings || 0 //收益
        this.updateAreaHero(data) //刷新区域英雄信息
    }

    public clean() {
        this.day = 0
    }

    public getDay() { return this.day }
    public getHour() { return this.hour }
    public getBattleAreas() { return this.battleAreas }

    public updateGold(val: number) {
        this.gold = val
    }

    // 获取英雄列表
    public getHeros() {
        const heros: HeroObj[] = []
        for (let k in this.battleAreas) {
            const hero = this.battleAreas[k]
            if (hero) {
                heros.push(hero)
            }
        }
        for (let k in this.prepareAreas) {
            const hero = this.prepareAreas[k]
            if (hero) {
                heros.push(hero)
            }
        }
        return heros
    }

    // 刷新区域英雄信息
    public updateAreaHero(data: any) {
        this.battleAreas = gHelper.initAreaData(data.battleAreas || {}) //战斗区域
        this.prepareAreas = gHelper.initAreaData(data.prepareAreas || {}) //备战区域
    }
}