package game

import (
	"casrv/server/common/pb"
	"casrv/server/game/battle"
	ut "casrv/utils"
	"casrv/utils/array"
)

type Area map[int8]*Hero

func (this Area) Add(hero *Hero, index int8, areaType int8) {
	hero.AreaType = areaType
	hero.Index = index
	this[index] = hero
}

func (this Area) Del(index int8) {
	delete(this, index)
}

// 玩家的信息
type PlayerInfo struct {
	UID         string `json:"uid"`
	Nickname    string `json:"nickname"`
	BattleArea  Area   `json:"battleArea"`  //战斗区域
	PrepareArea Area   `json:"prepareArea"` //备战区域
	HP          int32  `json:"hp"`          //生命
	RoleId      int32  `json:"roleId"`      //角色id
	Day         int32  `json:"day"`         //天数
	Hour        int32  `json:"hour"`        //小时
	WinCount    int32  `json:"winCount"`    //胜利次数
	Gold        int32  `json:"gold"`        //金币
	Earnings    int32  `json:"earnings"`    //收益
}

func (this *PlayerInfo) GetUID() string             { return this.UID }
func (this *PlayerInfo) GetBattleArea() battle.Area { return ToBattleArea(this.BattleArea) }
func (this *PlayerInfo) GetHP() int32               { return this.HP }
func (this *PlayerInfo) GetRoleId() int32           { return this.RoleId }

func NewPlayer(uid string, nickname string, roleId int32) *PlayerInfo {
	return &PlayerInfo{
		UID:         uid,
		Nickname:    nickname,
		RoleId:      roleId,
		BattleArea:  map[int8]*Hero{},
		PrepareArea: map[int8]*Hero{},
		Day:         1, //从第一天开始
		Hour:        0,
		HP:          INIT_HP,
		WinCount:    0,
		Gold:        INIT_GOLD,     //金币
		Earnings:    INIT_EARNINGS, //收益
	}
}

func (this *PlayerInfo) ToPb() *pb.PlayerInfo {
	return &pb.PlayerInfo{
		BattleArea:  ToAreaPb(this.BattleArea),
		PrepareArea: ToAreaPb(this.PrepareArea),
		Uid:         this.UID,
		Nickname:    this.Nickname,
		RoleId:      this.RoleId,
		Day:         this.Day,
		Hour:        this.Hour,
		Hp:          this.HP,
		WinCount:    this.WinCount,
		Gold:        this.Gold,
		Earnings:    this.Earnings,
	}
}

func (this *PlayerInfo) ToBasePb() *pb.PlayerInfo {
	return &pb.PlayerInfo{
		Day:      this.Day,
		Hour:     this.Hour,
		Hp:       this.HP,
		WinCount: this.WinCount,
	}
}

func (this *PlayerInfo) GetArea(tp int8) Area {
	switch tp {
	case AREA_TYPE_BATTLE:
		return this.BattleArea
	case AREA_TYPE_PREPARE:
		return this.PrepareArea
	}
	return nil
}

func (this *PlayerInfo) GetBattleAreaIdleSiteCount(areas Area) int8 {
	var count int8 = 0
	for _, area := range areas {
		count += ut.If[int8](area != nil, 1, 0)
	}
	return count
}

// 找出区域的空闲位置index
func (this *PlayerInfo) FindAreaIdleSiteIndex(areas Area, tp int8) int8 {
	for i, max := int8(0), AREA_SEAT_MAX_COUNT_MAP[tp]; i < max; i++ {
		if areas[i] == nil {
			return i
		}
	}
	return -1
}

// 检测区域类型位置标准
func (this *PlayerInfo) CheckAreaTypeAndIndex(areaType, index int8) bool {
	maxSeat := AREA_SEAT_MAX_COUNT_MAP[areaType]
	if maxSeat == 0 || index < 0 || index >= maxSeat {
		return false
	}
	return true
}

// 获取英雄
func (this *PlayerInfo) GetAreaHero(areas Area, uid string) *Hero {
	for _, m := range areas {
		if m.UID == uid {
			return m
		}
	}
	return nil
}

// 获取英雄 根据品质
func (this *PlayerInfo) GetAreaHeroById(areas Area, id int32) []*Hero {
	heros := []*Hero{}
	for _, m := range areas {
		if m.Id == id {
			heros = append(heros, m)
		}
	}
	return heros
}

// 获取同品质的英雄
func (this *PlayerInfo) GetHeroBySameQuality(id int32, quality int8) *Hero {
	heros := append(this.GetAreaHeroById(this.BattleArea, id), this.GetAreaHeroById(this.PrepareArea, id)...)
	if hero := array.Find(heros, func(m *Hero) bool { return m.Quality == quality }); hero != nil {
		return hero
	}
	return nil
}

// 获取英雄
func (this *PlayerInfo) GetHero(uid string) *Hero {
	if hero := this.GetAreaHero(this.BattleArea, uid); hero != nil {
		return hero
	}
	return this.GetAreaHero(this.PrepareArea, uid)
}

// 添加到空闲位置
func (this *PlayerInfo) AddHeroToIdleArea(hero *Hero) bool {
	// 先看战斗区域是否有空位 再看备战区域是否有位置
	if index := this.FindAreaIdleSiteIndex(this.BattleArea, AREA_TYPE_BATTLE); index != -1 {
		this.BattleArea.Add(hero, index, AREA_TYPE_BATTLE)
		return true
	} else if index := this.FindAreaIdleSiteIndex(this.PrepareArea, AREA_TYPE_PREPARE); index != -1 {
		this.PrepareArea.Add(hero, index, AREA_TYPE_PREPARE)
		return true
	}
	return false
}

// 添加英雄
func (this *PlayerInfo) AddHero(hero *Hero, areaType, index int8) bool {
	if sameHero := this.GetHeroBySameQuality(hero.Id, hero.Quality); sameHero != nil {
		sameHero.UpQuality()
		return true //如果有相同品质的英雄直接升级了
	} else if areaType == -1 && index == -1 {
		return this.AddHeroToIdleArea(hero)
	} else if !this.CheckAreaTypeAndIndex(areaType, index) {
		return false
	}
	area := this.GetArea(areaType)
	if area == nil {
		return false
	} else if area[index] != nil {
		return this.AddHeroToIdleArea(hero) //这个位置有人直接添加到空闲位置
	}
	// 直接添加
	area.Add(hero, index, areaType)
	return true
}

// 删除英雄
func (this *PlayerInfo) RemoveHero(hero *Hero) {
	if area := this.GetArea(hero.AreaType); area != nil {
		area.Del(hero.Index)
	}
}

// 移动英雄
func (this *PlayerInfo) MoveHero(hero *Hero, areaType, index int8) bool {
	if !this.CheckAreaTypeAndIndex(areaType, index) {
		return false
	} else if hero.AreaType == areaType && hero.Index == index {
		return false
	}
	area := this.GetArea(areaType)
	if area == nil {
		return false
	}
	// 上一个位置信息
	preAreaType, preIndex := hero.AreaType, hero.Index
	// 当前位置的英雄
	oldHero := area[index]
	// 先直接添加
	area.Add(hero, index, areaType)
	// 获取上一个的区域
	if areaType != preAreaType {
		area = this.GetArea(preAreaType)
	}
	if oldHero == nil {
		area.Del(preIndex) //当前位置没有人 就把上一个位置清空
	} else if oldHero.UID != hero.UID {
		area.Add(oldHero, preIndex, preAreaType) //如果有人就交互位置
	}
	return true
}

// 获取所有英雄map
func (this *PlayerInfo) GetAllHerosMap() map[int32][]*Hero {
	herosMap := map[int32][]*Hero{}
	for _, m := range this.BattleArea {
		if m == nil {
			continue
		} else if herosMap[m.Id] == nil {
			herosMap[m.Id] = []*Hero{}
		}
		herosMap[m.Id] = append(herosMap[m.Id], m)
	}
	for _, m := range this.PrepareArea {
		if m == nil {
			continue
		} else if herosMap[m.Id] == nil {
			herosMap[m.Id] = []*Hero{}
		}
		herosMap[m.Id] = append(herosMap[m.Id], m)
	}
	return herosMap
}
