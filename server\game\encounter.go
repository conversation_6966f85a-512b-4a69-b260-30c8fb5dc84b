package game

import (
	"casrv/server/common/pb"
	"casrv/server/game/battle"
	"casrv/utils/array"
)

// 酒馆
type TavernInfo struct {
	Id         int32   `json:"id"`         //id
	<PERSON><PERSON>      []*Hero `json:"heros"`      //英雄列表
	UpdateCost int32   `json:"updateCost"` //刷新费用 0.表示不可刷新
}

func (this *TavernInfo) ToPb() *pb.TavernInfo {
	return &pb.TavernInfo{
		Id:         this.Id,
		Heros:      array.Map(this.Heros, func(m *Hero, _ int) *pb.Hero { return m.ToPb() }),
		UpdateCost: this.UpdateCost,
	}
}

func (this *TavernInfo) GetHero(uid string) *Hero {
	if hero := array.Find(this.Heros, func(m *Hero) bool { return m.UID == uid }); hero != nil {
		return hero
	}
	return nil
}

// 敌人
type EnemyInfo struct {
	BattleArea map[int8]*Hero `json:"battleArea"` //战斗区域
}

func (this *EnemyInfo) ToPb() *pb.EnemyInfo {
	return &pb.EnemyInfo{
		BattleArea: ToAreaPb(this.BattleArea),
	}
}

func (this *EnemyInfo) GetUID() string             { return "enemy" }
func (this *EnemyInfo) GetBattleArea() battle.Area { return ToBattleArea(this.BattleArea) }
func (this *EnemyInfo) GetHP() int32               { return -1 }
func (this *EnemyInfo) GetRoleId() int32           { return -1 }

// 遭遇信息
type EncounterInfo struct {
	Player *PlayerInfo `json:"player"` //玩家信息
	Tavern *TavernInfo `json:"tavern"` //酒馆信息
	Enemy  *EnemyInfo  `json:"enemy"`  //敌人信息

	Type int32 `json:"type"` //类型
}

func (this *EncounterInfo) ToPb() *pb.EncounterInfo {
	info := &pb.EncounterInfo{Type: this.Type}
	switch this.Type {
	case MAP_NODE_TYPE_ENEMY_PLAYER:
		if this.Player != nil {
			info.Data_1 = this.Player.ToPb()
		}
	case MAP_NODE_TYPE_ENEMY_BATTLE:
		if this.Enemy != nil {
			info.Data_2 = this.Enemy.ToPb()
		}
	case MAP_NODE_TYPE_TAVERN:
		if this.Tavern != nil {
			info.Data_3 = this.Tavern.ToPb()
		}
	}
	return info
}

// 清理数据
func (this *EncounterInfo) CleanData() {
	this.Player = nil
	this.Enemy = nil
	this.Tavern = nil
}
