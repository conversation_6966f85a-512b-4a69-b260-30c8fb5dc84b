// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.9
// source: msg.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 二维坐标数据
type Vec2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X int32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"` //x轴
	Y int32 `protobuf:"varint,2,opt,name=y,proto3" json:"y,omitempty"` //y轴
}

func (x *Vec2) Reset() {
	*x = Vec2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Vec2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Vec2) ProtoMessage() {}

func (x *Vec2) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Vec2.ProtoReflect.Descriptor instead.
func (*Vec2) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{0}
}

func (x *Vec2) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *Vec2) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

// int32数组
type Int32ArrayInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Arr []int32 `protobuf:"varint,1,rep,packed,name=arr,proto3" json:"arr,omitempty"`
}

func (x *Int32ArrayInfo) Reset() {
	*x = Int32ArrayInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32ArrayInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32ArrayInfo) ProtoMessage() {}

func (x *Int32ArrayInfo) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32ArrayInfo.ProtoReflect.Descriptor instead.
func (*Int32ArrayInfo) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{1}
}

func (x *Int32ArrayInfo) GetArr() []int32 {
	if x != nil {
		return x.Arr
	}
	return nil
}

// 玩家数据
type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid            string  `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`                               //uid
	LoginType      string  `protobuf:"bytes,2,opt,name=loginType,proto3" json:"loginType,omitempty"`                   //登录方式
	TotalGameCount []int32 `protobuf:"varint,3,rep,packed,name=totalGameCount,proto3" json:"totalGameCount,omitempty"` //总局数
	LoginDayCount  int32   `protobuf:"varint,4,opt,name=loginDayCount,proto3" json:"loginDayCount,omitempty"`          //登录天数
	Nickname       string  `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname,omitempty"`                     //昵称
	CreateTime     int64   `protobuf:"varint,6,opt,name=createTime,proto3" json:"createTime,omitempty"`                //创建时间
	SumOnlineTime  int64   `protobuf:"varint,7,opt,name=sumOnlineTime,proto3" json:"sumOnlineTime,omitempty"`          //累计在线时间
	SessionId      string  `protobuf:"bytes,8,opt,name=sessionId,proto3" json:"sessionId,omitempty"`                   //每次登录唯一id
	RoleId         int32   `protobuf:"varint,9,opt,name=roleId,proto3" json:"roleId,omitempty"`                        //当前使用的角色id
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{2}
}

func (x *UserInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *UserInfo) GetLoginType() string {
	if x != nil {
		return x.LoginType
	}
	return ""
}

func (x *UserInfo) GetTotalGameCount() []int32 {
	if x != nil {
		return x.TotalGameCount
	}
	return nil
}

func (x *UserInfo) GetLoginDayCount() int32 {
	if x != nil {
		return x.LoginDayCount
	}
	return 0
}

func (x *UserInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UserInfo) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UserInfo) GetSumOnlineTime() int64 {
	if x != nil {
		return x.SumOnlineTime
	}
	return 0
}

func (x *UserInfo) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *UserInfo) GetRoleId() int32 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

// 一个英雄
type Hero struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid        string            `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id         int32             `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Quality    int32             `protobuf:"varint,3,opt,name=quality,proto3" json:"quality,omitempty"`      //品质
	ExtraAttrs []*Int32ArrayInfo `protobuf:"bytes,4,rep,name=extraAttrs,proto3" json:"extraAttrs,omitempty"` //额外属性
	Cost       int32             `protobuf:"varint,5,opt,name=cost,proto3" json:"cost,omitempty"`            //费用
	AreaType   int32             `protobuf:"varint,6,opt,name=areaType,proto3" json:"areaType,omitempty"`    //区域类型
	Index      int32             `protobuf:"varint,7,opt,name=index,proto3" json:"index,omitempty"`          //位置
}

func (x *Hero) Reset() {
	*x = Hero{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Hero) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Hero) ProtoMessage() {}

func (x *Hero) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Hero.ProtoReflect.Descriptor instead.
func (*Hero) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{3}
}

func (x *Hero) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Hero) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Hero) GetQuality() int32 {
	if x != nil {
		return x.Quality
	}
	return 0
}

func (x *Hero) GetExtraAttrs() []*Int32ArrayInfo {
	if x != nil {
		return x.ExtraAttrs
	}
	return nil
}

func (x *Hero) GetCost() int32 {
	if x != nil {
		return x.Cost
	}
	return 0
}

func (x *Hero) GetAreaType() int32 {
	if x != nil {
		return x.AreaType
	}
	return 0
}

func (x *Hero) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

// 酒馆
type TavernInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int32   `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`      //id
	Heros      []*Hero `protobuf:"bytes,2,rep,name=heros,proto3" json:"heros,omitempty"` //英雄列表
	UpdateCost int32   `protobuf:"varint,3,opt,name=updateCost,proto3" json:"updateCost,omitempty"`
}

func (x *TavernInfo) Reset() {
	*x = TavernInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TavernInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TavernInfo) ProtoMessage() {}

func (x *TavernInfo) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TavernInfo.ProtoReflect.Descriptor instead.
func (*TavernInfo) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{4}
}

func (x *TavernInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TavernInfo) GetHeros() []*Hero {
	if x != nil {
		return x.Heros
	}
	return nil
}

func (x *TavernInfo) GetUpdateCost() int32 {
	if x != nil {
		return x.UpdateCost
	}
	return 0
}

// 敌人
type EnemyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BattleArea map[int32]*Hero `protobuf:"bytes,1,rep,name=battleArea,proto3" json:"battleArea,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //战斗区域
}

func (x *EnemyInfo) Reset() {
	*x = EnemyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnemyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnemyInfo) ProtoMessage() {}

func (x *EnemyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnemyInfo.ProtoReflect.Descriptor instead.
func (*EnemyInfo) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{5}
}

func (x *EnemyInfo) GetBattleArea() map[int32]*Hero {
	if x != nil {
		return x.BattleArea
	}
	return nil
}

// 遭遇信息
type EncounterInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       int32       `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	UpdateCost int32       `protobuf:"varint,2,opt,name=updateCost,proto3" json:"updateCost,omitempty"`   //刷新费用
	Data_1     *PlayerInfo `protobuf:"bytes,3,opt,name=data_1,json=data1,proto3" json:"data_1,omitempty"` //玩家信息
	Data_2     *EnemyInfo  `protobuf:"bytes,4,opt,name=data_2,json=data2,proto3" json:"data_2,omitempty"` //敌人信息
	Data_3     *TavernInfo `protobuf:"bytes,5,opt,name=data_3,json=data3,proto3" json:"data_3,omitempty"` //酒馆信息
}

func (x *EncounterInfo) Reset() {
	*x = EncounterInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EncounterInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EncounterInfo) ProtoMessage() {}

func (x *EncounterInfo) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EncounterInfo.ProtoReflect.Descriptor instead.
func (*EncounterInfo) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{6}
}

func (x *EncounterInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *EncounterInfo) GetUpdateCost() int32 {
	if x != nil {
		return x.UpdateCost
	}
	return 0
}

func (x *EncounterInfo) GetData_1() *PlayerInfo {
	if x != nil {
		return x.Data_1
	}
	return nil
}

func (x *EncounterInfo) GetData_2() *EnemyInfo {
	if x != nil {
		return x.Data_2
	}
	return nil
}

func (x *EncounterInfo) GetData_3() *TavernInfo {
	if x != nil {
		return x.Data_3
	}
	return nil
}

// 玩家信息
type PlayerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid         string          `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname    string          `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	BattleArea  map[int32]*Hero `protobuf:"bytes,3,rep,name=battleArea,proto3" json:"battleArea,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`   //战斗区域
	PrepareArea map[int32]*Hero `protobuf:"bytes,4,rep,name=prepareArea,proto3" json:"prepareArea,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //备战区域
	RoleId      int32           `protobuf:"varint,5,opt,name=roleId,proto3" json:"roleId,omitempty"`                                                                                                   //角色id
	Day         int32           `protobuf:"varint,6,opt,name=day,proto3" json:"day,omitempty"`                                                                                                         //天数
	Hour        int32           `protobuf:"varint,7,opt,name=hour,proto3" json:"hour,omitempty"`                                                                                                       //小时
	Hp          int32           `protobuf:"varint,8,opt,name=hp,proto3" json:"hp,omitempty"`                                                                                                           //血量
	WinCount    int32           `protobuf:"varint,9,opt,name=winCount,proto3" json:"winCount,omitempty"`                                                                                               //胜利次数
	Gold        int32           `protobuf:"varint,10,opt,name=gold,proto3" json:"gold,omitempty"`                                                                                                      //金币
	Earnings    int32           `protobuf:"varint,11,opt,name=earnings,proto3" json:"earnings,omitempty"`                                                                                              //收益
}

func (x *PlayerInfo) Reset() {
	*x = PlayerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerInfo) ProtoMessage() {}

func (x *PlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerInfo.ProtoReflect.Descriptor instead.
func (*PlayerInfo) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{7}
}

func (x *PlayerInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *PlayerInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *PlayerInfo) GetBattleArea() map[int32]*Hero {
	if x != nil {
		return x.BattleArea
	}
	return nil
}

func (x *PlayerInfo) GetPrepareArea() map[int32]*Hero {
	if x != nil {
		return x.PrepareArea
	}
	return nil
}

func (x *PlayerInfo) GetRoleId() int32 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *PlayerInfo) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *PlayerInfo) GetHour() int32 {
	if x != nil {
		return x.Hour
	}
	return 0
}

func (x *PlayerInfo) GetHp() int32 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *PlayerInfo) GetWinCount() int32 {
	if x != nil {
		return x.WinCount
	}
	return 0
}

func (x *PlayerInfo) GetGold() int32 {
	if x != nil {
		return x.Gold
	}
	return 0
}

func (x *PlayerInfo) GetEarnings() int32 {
	if x != nil {
		return x.Earnings
	}
	return 0
}

// 游戏数据
type GameData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Player    *PlayerInfo    `protobuf:"bytes,1,opt,name=player,proto3" json:"player,omitempty"`             //自己的信息
	Encounter *EncounterInfo `protobuf:"bytes,2,opt,name=encounter,proto3" json:"encounter,omitempty"`       //遭遇信息
	MapPaths  []int32        `protobuf:"varint,3,rep,packed,name=mapPaths,proto3" json:"mapPaths,omitempty"` //地图路径
}

func (x *GameData) Reset() {
	*x = GameData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameData) ProtoMessage() {}

func (x *GameData) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameData.ProtoReflect.Descriptor instead.
func (*GameData) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{8}
}

func (x *GameData) GetPlayer() *PlayerInfo {
	if x != nil {
		return x.Player
	}
	return nil
}

func (x *GameData) GetEncounter() *EncounterInfo {
	if x != nil {
		return x.Encounter
	}
	return nil
}

func (x *GameData) GetMapPaths() []int32 {
	if x != nil {
		return x.MapPaths
	}
	return nil
}

// 地图节点
type MapNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeId   int32   `protobuf:"varint,1,opt,name=nodeId,proto3" json:"nodeId,omitempty"`            //节点id
	Children []int32 `protobuf:"varint,2,rep,packed,name=children,proto3" json:"children,omitempty"` //可前往的节点id
}

func (x *MapNode) Reset() {
	*x = MapNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MapNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapNode) ProtoMessage() {}

func (x *MapNode) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapNode.ProtoReflect.Descriptor instead.
func (*MapNode) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{9}
}

func (x *MapNode) GetNodeId() int32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (x *MapNode) GetChildren() []int32 {
	if x != nil {
		return x.Children
	}
	return nil
}

// 一层
type MapLayer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []*MapNode `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"` //节点列表
}

func (x *MapLayer) Reset() {
	*x = MapLayer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MapLayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapLayer) ProtoMessage() {}

func (x *MapLayer) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapLayer.ProtoReflect.Descriptor instead.
func (*MapLayer) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{10}
}

func (x *MapLayer) GetNodes() []*MapNode {
	if x != nil {
		return x.Nodes
	}
	return nil
}

// 地图数据
type MapData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Maps []*MapLayer `protobuf:"bytes,1,rep,name=maps,proto3" json:"maps,omitempty"` //地图数据
}

func (x *MapData) Reset() {
	*x = MapData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MapData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapData) ProtoMessage() {}

func (x *MapData) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapData.ProtoReflect.Descriptor instead.
func (*MapData) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{11}
}

func (x *MapData) GetMaps() []*MapLayer {
	if x != nil {
		return x.Maps
	}
	return nil
}

// 服务器返回协议
type S2C_RESULT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data  []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Error string `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *S2C_RESULT) Reset() {
	*x = S2C_RESULT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_RESULT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_RESULT) ProtoMessage() {}

func (x *S2C_RESULT) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_RESULT.ProtoReflect.Descriptor instead.
func (*S2C_RESULT) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{12}
}

func (x *S2C_RESULT) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *S2C_RESULT) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// 游客登录
type LOGIN_HD_GUESTLOGIN_C2S struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuestId  string `protobuf:"bytes,1,opt,name=guestId,proto3" json:"guestId,omitempty"`   //游客id
	Nickname string `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"` //昵称
	Platform string `protobuf:"bytes,3,opt,name=platform,proto3" json:"platform,omitempty"` //平台
}

func (x *LOGIN_HD_GUESTLOGIN_C2S) Reset() {
	*x = LOGIN_HD_GUESTLOGIN_C2S{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LOGIN_HD_GUESTLOGIN_C2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LOGIN_HD_GUESTLOGIN_C2S) ProtoMessage() {}

func (x *LOGIN_HD_GUESTLOGIN_C2S) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LOGIN_HD_GUESTLOGIN_C2S.ProtoReflect.Descriptor instead.
func (*LOGIN_HD_GUESTLOGIN_C2S) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{13}
}

func (x *LOGIN_HD_GUESTLOGIN_C2S) GetGuestId() string {
	if x != nil {
		return x.GuestId
	}
	return ""
}

func (x *LOGIN_HD_GUESTLOGIN_C2S) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *LOGIN_HD_GUESTLOGIN_C2S) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

// 游客登录返回
type LOGIN_HD_GUESTLOGIN_S2C struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountToken string `protobuf:"bytes,1,opt,name=accountToken,proto3" json:"accountToken,omitempty"` //登录token
	GuestId      string `protobuf:"bytes,2,opt,name=guestId,proto3" json:"guestId,omitempty"`           //游客id
}

func (x *LOGIN_HD_GUESTLOGIN_S2C) Reset() {
	*x = LOGIN_HD_GUESTLOGIN_S2C{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LOGIN_HD_GUESTLOGIN_S2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LOGIN_HD_GUESTLOGIN_S2C) ProtoMessage() {}

func (x *LOGIN_HD_GUESTLOGIN_S2C) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LOGIN_HD_GUESTLOGIN_S2C.ProtoReflect.Descriptor instead.
func (*LOGIN_HD_GUESTLOGIN_S2C) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{14}
}

func (x *LOGIN_HD_GUESTLOGIN_S2C) GetAccountToken() string {
	if x != nil {
		return x.AccountToken
	}
	return ""
}

func (x *LOGIN_HD_GUESTLOGIN_S2C) GetGuestId() string {
	if x != nil {
		return x.GuestId
	}
	return ""
}

// 尝试登录
type LOBBY_HD_TRYLOGIN_C2S struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountToken string `protobuf:"bytes,1,opt,name=accountToken,proto3" json:"accountToken,omitempty"` //登录token
	Lang         string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`                 //语言
	Platform     string `protobuf:"bytes,3,opt,name=platform,proto3" json:"platform,omitempty"`         //平台
	Version      string `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`           //版本
}

func (x *LOBBY_HD_TRYLOGIN_C2S) Reset() {
	*x = LOBBY_HD_TRYLOGIN_C2S{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LOBBY_HD_TRYLOGIN_C2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LOBBY_HD_TRYLOGIN_C2S) ProtoMessage() {}

func (x *LOBBY_HD_TRYLOGIN_C2S) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LOBBY_HD_TRYLOGIN_C2S.ProtoReflect.Descriptor instead.
func (*LOBBY_HD_TRYLOGIN_C2S) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{15}
}

func (x *LOBBY_HD_TRYLOGIN_C2S) GetAccountToken() string {
	if x != nil {
		return x.AccountToken
	}
	return ""
}

func (x *LOBBY_HD_TRYLOGIN_C2S) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *LOBBY_HD_TRYLOGIN_C2S) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *LOBBY_HD_TRYLOGIN_C2S) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// 尝试登录 返回
type LOBBY_HD_TRYLOGIN_S2C struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User                  *UserInfo   `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`                                    //玩家信息
	AccountToken          string      `protobuf:"bytes,2,opt,name=accountToken,proto3" json:"accountToken,omitempty"`                    //登录token
	BanAccountType        int32       `protobuf:"varint,3,opt,name=banAccountType,proto3" json:"banAccountType,omitempty"`               //封禁类型
	BanAccountSurplusTime int64       `protobuf:"varint,4,opt,name=banAccountSurplusTime,proto3" json:"banAccountSurplusTime,omitempty"` //封禁剩余时间
	GameBaseData          *PlayerInfo `protobuf:"bytes,5,opt,name=gameBaseData,proto3" json:"gameBaseData,omitempty"`                    //游戏基础数据
}

func (x *LOBBY_HD_TRYLOGIN_S2C) Reset() {
	*x = LOBBY_HD_TRYLOGIN_S2C{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LOBBY_HD_TRYLOGIN_S2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LOBBY_HD_TRYLOGIN_S2C) ProtoMessage() {}

func (x *LOBBY_HD_TRYLOGIN_S2C) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LOBBY_HD_TRYLOGIN_S2C.ProtoReflect.Descriptor instead.
func (*LOBBY_HD_TRYLOGIN_S2C) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{16}
}

func (x *LOBBY_HD_TRYLOGIN_S2C) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *LOBBY_HD_TRYLOGIN_S2C) GetAccountToken() string {
	if x != nil {
		return x.AccountToken
	}
	return ""
}

func (x *LOBBY_HD_TRYLOGIN_S2C) GetBanAccountType() int32 {
	if x != nil {
		return x.BanAccountType
	}
	return 0
}

func (x *LOBBY_HD_TRYLOGIN_S2C) GetBanAccountSurplusTime() int64 {
	if x != nil {
		return x.BanAccountSurplusTime
	}
	return 0
}

func (x *LOBBY_HD_TRYLOGIN_S2C) GetGameBaseData() *PlayerInfo {
	if x != nil {
		return x.GameBaseData
	}
	return nil
}

// 进入游戏
type GAME_HD_ENTRY_C2S struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nickname string `protobuf:"bytes,1,opt,name=nickname,proto3" json:"nickname,omitempty"`
	RoleId   int32  `protobuf:"varint,2,opt,name=roleId,proto3" json:"roleId,omitempty"` //角色id
}

func (x *GAME_HD_ENTRY_C2S) Reset() {
	*x = GAME_HD_ENTRY_C2S{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GAME_HD_ENTRY_C2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GAME_HD_ENTRY_C2S) ProtoMessage() {}

func (x *GAME_HD_ENTRY_C2S) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GAME_HD_ENTRY_C2S.ProtoReflect.Descriptor instead.
func (*GAME_HD_ENTRY_C2S) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{17}
}

func (x *GAME_HD_ENTRY_C2S) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *GAME_HD_ENTRY_C2S) GetRoleId() int32 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

type GAME_HD_ENTRY_S2C struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapData  *MapData  `protobuf:"bytes,1,opt,name=mapData,proto3" json:"mapData,omitempty"`   //地图数据
	GameData *GameData `protobuf:"bytes,2,opt,name=gameData,proto3" json:"gameData,omitempty"` //游戏数据
}

func (x *GAME_HD_ENTRY_S2C) Reset() {
	*x = GAME_HD_ENTRY_S2C{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GAME_HD_ENTRY_S2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GAME_HD_ENTRY_S2C) ProtoMessage() {}

func (x *GAME_HD_ENTRY_S2C) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GAME_HD_ENTRY_S2C.ProtoReflect.Descriptor instead.
func (*GAME_HD_ENTRY_S2C) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{18}
}

func (x *GAME_HD_ENTRY_S2C) GetMapData() *MapData {
	if x != nil {
		return x.MapData
	}
	return nil
}

func (x *GAME_HD_ENTRY_S2C) GetGameData() *GameData {
	if x != nil {
		return x.GameData
	}
	return nil
}

// 选择地图节点
type GAME_HD_SELECTMAPNODE_C2S struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"` //选择的物品索引
}

func (x *GAME_HD_SELECTMAPNODE_C2S) Reset() {
	*x = GAME_HD_SELECTMAPNODE_C2S{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GAME_HD_SELECTMAPNODE_C2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GAME_HD_SELECTMAPNODE_C2S) ProtoMessage() {}

func (x *GAME_HD_SELECTMAPNODE_C2S) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GAME_HD_SELECTMAPNODE_C2S.ProtoReflect.Descriptor instead.
func (*GAME_HD_SELECTMAPNODE_C2S) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{19}
}

func (x *GAME_HD_SELECTMAPNODE_C2S) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type GAME_HD_SELECTMAPNODE_S2C struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameData *GameData `protobuf:"bytes,1,opt,name=gameData,proto3" json:"gameData,omitempty"` //游戏数据
}

func (x *GAME_HD_SELECTMAPNODE_S2C) Reset() {
	*x = GAME_HD_SELECTMAPNODE_S2C{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GAME_HD_SELECTMAPNODE_S2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GAME_HD_SELECTMAPNODE_S2C) ProtoMessage() {}

func (x *GAME_HD_SELECTMAPNODE_S2C) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GAME_HD_SELECTMAPNODE_S2C.ProtoReflect.Descriptor instead.
func (*GAME_HD_SELECTMAPNODE_S2C) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{20}
}

func (x *GAME_HD_SELECTMAPNODE_S2C) GetGameData() *GameData {
	if x != nil {
		return x.GameData
	}
	return nil
}

// 获取下一天的地图信息
type GAME_HD_GETNEXTDAYMAP_C2S struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GAME_HD_GETNEXTDAYMAP_C2S) Reset() {
	*x = GAME_HD_GETNEXTDAYMAP_C2S{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GAME_HD_GETNEXTDAYMAP_C2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GAME_HD_GETNEXTDAYMAP_C2S) ProtoMessage() {}

func (x *GAME_HD_GETNEXTDAYMAP_C2S) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GAME_HD_GETNEXTDAYMAP_C2S.ProtoReflect.Descriptor instead.
func (*GAME_HD_GETNEXTDAYMAP_C2S) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{21}
}

type GAME_HD_GETNEXTDAYMAP_S2C struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapData  *MapData  `protobuf:"bytes,1,opt,name=mapData,proto3" json:"mapData,omitempty"`   //地图数据
	GameData *GameData `protobuf:"bytes,2,opt,name=gameData,proto3" json:"gameData,omitempty"` //游戏数据
}

func (x *GAME_HD_GETNEXTDAYMAP_S2C) Reset() {
	*x = GAME_HD_GETNEXTDAYMAP_S2C{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GAME_HD_GETNEXTDAYMAP_S2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GAME_HD_GETNEXTDAYMAP_S2C) ProtoMessage() {}

func (x *GAME_HD_GETNEXTDAYMAP_S2C) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GAME_HD_GETNEXTDAYMAP_S2C.ProtoReflect.Descriptor instead.
func (*GAME_HD_GETNEXTDAYMAP_S2C) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{22}
}

func (x *GAME_HD_GETNEXTDAYMAP_S2C) GetMapData() *MapData {
	if x != nil {
		return x.MapData
	}
	return nil
}

func (x *GAME_HD_GETNEXTDAYMAP_S2C) GetGameData() *GameData {
	if x != nil {
		return x.GameData
	}
	return nil
}

// 购买英雄
type GAME_HD_BUYHERO_C2S struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeroUid  string `protobuf:"bytes,1,opt,name=heroUid,proto3" json:"heroUid,omitempty"`    //选择的英雄的uid
	AreaType int32  `protobuf:"varint,2,opt,name=areaType,proto3" json:"areaType,omitempty"` //区域类型
	UseIndex int32  `protobuf:"varint,3,opt,name=useIndex,proto3" json:"useIndex,omitempty"` //使用位置
}

func (x *GAME_HD_BUYHERO_C2S) Reset() {
	*x = GAME_HD_BUYHERO_C2S{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GAME_HD_BUYHERO_C2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GAME_HD_BUYHERO_C2S) ProtoMessage() {}

func (x *GAME_HD_BUYHERO_C2S) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GAME_HD_BUYHERO_C2S.ProtoReflect.Descriptor instead.
func (*GAME_HD_BUYHERO_C2S) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{23}
}

func (x *GAME_HD_BUYHERO_C2S) GetHeroUid() string {
	if x != nil {
		return x.HeroUid
	}
	return ""
}

func (x *GAME_HD_BUYHERO_C2S) GetAreaType() int32 {
	if x != nil {
		return x.AreaType
	}
	return 0
}

func (x *GAME_HD_BUYHERO_C2S) GetUseIndex() int32 {
	if x != nil {
		return x.UseIndex
	}
	return 0
}

type GAME_HD_BUYHERO_S2C struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameData *GameData `protobuf:"bytes,1,opt,name=gameData,proto3" json:"gameData,omitempty"` //游戏数据
}

func (x *GAME_HD_BUYHERO_S2C) Reset() {
	*x = GAME_HD_BUYHERO_S2C{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GAME_HD_BUYHERO_S2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GAME_HD_BUYHERO_S2C) ProtoMessage() {}

func (x *GAME_HD_BUYHERO_S2C) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GAME_HD_BUYHERO_S2C.ProtoReflect.Descriptor instead.
func (*GAME_HD_BUYHERO_S2C) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{24}
}

func (x *GAME_HD_BUYHERO_S2C) GetGameData() *GameData {
	if x != nil {
		return x.GameData
	}
	return nil
}

// 出售英雄
type GAME_HD_SELLHERO_C2S struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeroUid string `protobuf:"bytes,1,opt,name=heroUid,proto3" json:"heroUid,omitempty"` //选择的英雄的uid
}

func (x *GAME_HD_SELLHERO_C2S) Reset() {
	*x = GAME_HD_SELLHERO_C2S{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GAME_HD_SELLHERO_C2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GAME_HD_SELLHERO_C2S) ProtoMessage() {}

func (x *GAME_HD_SELLHERO_C2S) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GAME_HD_SELLHERO_C2S.ProtoReflect.Descriptor instead.
func (*GAME_HD_SELLHERO_C2S) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{25}
}

func (x *GAME_HD_SELLHERO_C2S) GetHeroUid() string {
	if x != nil {
		return x.HeroUid
	}
	return ""
}

type GAME_HD_SELLHERO_S2C struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BattleArea  map[int32]*Hero `protobuf:"bytes,1,rep,name=battleArea,proto3" json:"battleArea,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`   //战斗区域
	PrepareArea map[int32]*Hero `protobuf:"bytes,2,rep,name=prepareArea,proto3" json:"prepareArea,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //备战区域
	Gold        int32           `protobuf:"varint,3,opt,name=gold,proto3" json:"gold,omitempty"`                                                                                                       //金币
}

func (x *GAME_HD_SELLHERO_S2C) Reset() {
	*x = GAME_HD_SELLHERO_S2C{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GAME_HD_SELLHERO_S2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GAME_HD_SELLHERO_S2C) ProtoMessage() {}

func (x *GAME_HD_SELLHERO_S2C) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GAME_HD_SELLHERO_S2C.ProtoReflect.Descriptor instead.
func (*GAME_HD_SELLHERO_S2C) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{26}
}

func (x *GAME_HD_SELLHERO_S2C) GetBattleArea() map[int32]*Hero {
	if x != nil {
		return x.BattleArea
	}
	return nil
}

func (x *GAME_HD_SELLHERO_S2C) GetPrepareArea() map[int32]*Hero {
	if x != nil {
		return x.PrepareArea
	}
	return nil
}

func (x *GAME_HD_SELLHERO_S2C) GetGold() int32 {
	if x != nil {
		return x.Gold
	}
	return 0
}

// 移动英雄
type GAME_HD_MOVEHERO_C2S struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeroUid  string `protobuf:"bytes,1,opt,name=heroUid,proto3" json:"heroUid,omitempty"`    //选择的英雄的uid
	AreaType int32  `protobuf:"varint,2,opt,name=areaType,proto3" json:"areaType,omitempty"` //区域类型
	UseIndex int32  `protobuf:"varint,3,opt,name=useIndex,proto3" json:"useIndex,omitempty"` //使用位置
}

func (x *GAME_HD_MOVEHERO_C2S) Reset() {
	*x = GAME_HD_MOVEHERO_C2S{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GAME_HD_MOVEHERO_C2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GAME_HD_MOVEHERO_C2S) ProtoMessage() {}

func (x *GAME_HD_MOVEHERO_C2S) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GAME_HD_MOVEHERO_C2S.ProtoReflect.Descriptor instead.
func (*GAME_HD_MOVEHERO_C2S) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{27}
}

func (x *GAME_HD_MOVEHERO_C2S) GetHeroUid() string {
	if x != nil {
		return x.HeroUid
	}
	return ""
}

func (x *GAME_HD_MOVEHERO_C2S) GetAreaType() int32 {
	if x != nil {
		return x.AreaType
	}
	return 0
}

func (x *GAME_HD_MOVEHERO_C2S) GetUseIndex() int32 {
	if x != nil {
		return x.UseIndex
	}
	return 0
}

type GAME_HD_MOVEHERO_S2C struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BattleArea  map[int32]*Hero `protobuf:"bytes,1,rep,name=battleArea,proto3" json:"battleArea,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`   //战斗区域
	PrepareArea map[int32]*Hero `protobuf:"bytes,2,rep,name=prepareArea,proto3" json:"prepareArea,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //备战区域
}

func (x *GAME_HD_MOVEHERO_S2C) Reset() {
	*x = GAME_HD_MOVEHERO_S2C{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GAME_HD_MOVEHERO_S2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GAME_HD_MOVEHERO_S2C) ProtoMessage() {}

func (x *GAME_HD_MOVEHERO_S2C) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GAME_HD_MOVEHERO_S2C.ProtoReflect.Descriptor instead.
func (*GAME_HD_MOVEHERO_S2C) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{28}
}

func (x *GAME_HD_MOVEHERO_S2C) GetBattleArea() map[int32]*Hero {
	if x != nil {
		return x.BattleArea
	}
	return nil
}

func (x *GAME_HD_MOVEHERO_S2C) GetPrepareArea() map[int32]*Hero {
	if x != nil {
		return x.PrepareArea
	}
	return nil
}

// 用户数据更新通知
type LOBBY_ONUPDATEUSERINFO_NOTIFY struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*OnUpdatePlayerInfoNotify `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"` //玩家数据更新通知列表
}

func (x *LOBBY_ONUPDATEUSERINFO_NOTIFY) Reset() {
	*x = LOBBY_ONUPDATEUSERINFO_NOTIFY{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LOBBY_ONUPDATEUSERINFO_NOTIFY) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LOBBY_ONUPDATEUSERINFO_NOTIFY) ProtoMessage() {}

func (x *LOBBY_ONUPDATEUSERINFO_NOTIFY) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LOBBY_ONUPDATEUSERINFO_NOTIFY.ProtoReflect.Descriptor instead.
func (*LOBBY_ONUPDATEUSERINFO_NOTIFY) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{29}
}

func (x *LOBBY_ONUPDATEUSERINFO_NOTIFY) GetList() []*OnUpdatePlayerInfoNotify {
	if x != nil {
		return x.List
	}
	return nil
}

// 玩家数据更新通知数据
type OnUpdatePlayerInfoNotify struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"` //类型
}

func (x *OnUpdatePlayerInfoNotify) Reset() {
	*x = OnUpdatePlayerInfoNotify{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnUpdatePlayerInfoNotify) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnUpdatePlayerInfoNotify) ProtoMessage() {}

func (x *OnUpdatePlayerInfoNotify) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnUpdatePlayerInfoNotify.ProtoReflect.Descriptor instead.
func (*OnUpdatePlayerInfoNotify) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{30}
}

func (x *OnUpdatePlayerInfoNotify) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

// 主动踢下线通知
type GAME_ONKICK_NOTIFY struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	BanType int32 `protobuf:"varint,2,opt,name=banType,proto3" json:"banType,omitempty"`
}

func (x *GAME_ONKICK_NOTIFY) Reset() {
	*x = GAME_ONKICK_NOTIFY{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GAME_ONKICK_NOTIFY) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GAME_ONKICK_NOTIFY) ProtoMessage() {}

func (x *GAME_ONKICK_NOTIFY) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GAME_ONKICK_NOTIFY.ProtoReflect.Descriptor instead.
func (*GAME_ONKICK_NOTIFY) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{31}
}

func (x *GAME_ONKICK_NOTIFY) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *GAME_ONKICK_NOTIFY) GetBanType() int32 {
	if x != nil {
		return x.BanType
	}
	return 0
}

var File_msg_proto protoreflect.FileDescriptor

var file_msg_proto_rawDesc = []byte{
	0x0a, 0x09, 0x6d, 0x73, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x22, 0x0a, 0x04, 0x56, 0x65, 0x63, 0x32, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x01, 0x79, 0x22, 0x22, 0x0a, 0x0e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x41,
	0x72, 0x72, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x72, 0x72, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x03, 0x61, 0x72, 0x72, 0x22, 0xa0, 0x02, 0x0a, 0x08, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x67,
	0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f,
	0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x24, 0x0a, 0x0d, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x44, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x44, 0x61, 0x79,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x75, 0x6d, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x73, 0x75, 0x6d, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x22, 0xbf, 0x01,
	0x0a, 0x04, 0x48, 0x65, 0x72, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x71, 0x75, 0x61, 0x6c,
	0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x71, 0x75, 0x61, 0x6c, 0x69,
	0x74, 0x79, 0x12, 0x35, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x41, 0x74, 0x74, 0x72, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x41, 0x72, 0x72, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x41, 0x74, 0x74, 0x72, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x73,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x61, 0x72, 0x65, 0x61, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x61, 0x72, 0x65, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22,
	0x5f, 0x0a, 0x0a, 0x54, 0x61, 0x76, 0x65, 0x72, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a,
	0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x05, 0x68, 0x65, 0x72, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x72, 0x6f, 0x52, 0x05, 0x68, 0x65, 0x72, 0x6f, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x73, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x73, 0x74,
	0x22, 0x99, 0x01, 0x0a, 0x09, 0x45, 0x6e, 0x65, 0x6d, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x40,
	0x0a, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x72, 0x65, 0x61, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x6e, 0x65, 0x6d, 0x79,
	0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x72, 0x65, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x72, 0x65, 0x61,
	0x1a, 0x4a, 0x0a, 0x0f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x72, 0x65, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x72,
	0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc0, 0x01, 0x0a,
	0x0d, 0x45, 0x6e, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x73, 0x74, 0x12, 0x28, 0x0a, 0x06, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x31, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x64, 0x61, 0x74, 0x61, 0x31, 0x12, 0x27, 0x0a, 0x06,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x6e, 0x65, 0x6d, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05,
	0x64, 0x61, 0x74, 0x61, 0x32, 0x12, 0x28, 0x0a, 0x06, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x33, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x61,
	0x76, 0x65, 0x72, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x64, 0x61, 0x74, 0x61, 0x33, 0x22,
	0xf6, 0x03, 0x0a, 0x0a, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0a,
	0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x72, 0x65, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x72, 0x65, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x72, 0x65, 0x61, 0x12,
	0x44, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x41, 0x72, 0x65, 0x61, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x41,
	0x72, 0x65, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x70, 0x61, 0x72,
	0x65, 0x41, 0x72, 0x65, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x64, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x64, 0x61, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x68,
	0x6f, 0x75, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x68, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x68, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x77, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x67, 0x6f, 0x6c, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x67,
	0x6f, 0x6c, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x1a,
	0x4a, 0x0a, 0x0f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x72, 0x65, 0x61, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x72, 0x6f,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x4b, 0x0a, 0x10, 0x50,
	0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x41, 0x72, 0x65, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x21, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x72, 0x6f, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x85, 0x01, 0x0a, 0x08, 0x47, 0x61, 0x6d,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x29, 0x0a, 0x06, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x12, 0x32, 0x0a, 0x09, 0x65, 0x6e, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x6e, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x65, 0x6e, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x70, 0x50, 0x61, 0x74, 0x68, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x70, 0x50, 0x61, 0x74, 0x68, 0x73,
	0x22, 0x3d, 0x0a, 0x07, 0x4d, 0x61, 0x70, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e,
	0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x6f, 0x64,
	0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x22,
	0x30, 0x0a, 0x08, 0x4d, 0x61, 0x70, 0x4c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x05, 0x6e,
	0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x70, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65,
	0x73, 0x22, 0x2e, 0x0a, 0x07, 0x4d, 0x61, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x04,
	0x6d, 0x61, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x70, 0x4c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x04, 0x6d, 0x61, 0x70,
	0x73, 0x22, 0x36, 0x0a, 0x0a, 0x53, 0x32, 0x43, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x6b, 0x0a, 0x17, 0x4c, 0x4f, 0x47,
	0x49, 0x4e, 0x5f, 0x48, 0x44, 0x5f, 0x47, 0x55, 0x45, 0x53, 0x54, 0x4c, 0x4f, 0x47, 0x49, 0x4e,
	0x5f, 0x43, 0x32, 0x53, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0x57, 0x0a, 0x17, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f,
	0x48, 0x44, 0x5f, 0x47, 0x55, 0x45, 0x53, 0x54, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x53, 0x32,
	0x43, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22,
	0x85, 0x01, 0x0a, 0x15, 0x4c, 0x4f, 0x42, 0x42, 0x59, 0x5f, 0x48, 0x44, 0x5f, 0x54, 0x52, 0x59,
	0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x43, 0x32, 0x53, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e,
	0x67, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xf5, 0x01, 0x0a, 0x15, 0x4c, 0x4f, 0x42, 0x42,
	0x59, 0x5f, 0x48, 0x44, 0x5f, 0x54, 0x52, 0x59, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x53, 0x32,
	0x43, 0x12, 0x23, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x62, 0x61,
	0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x62, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x15, 0x62, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x72,
	0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x0c, 0x67, 0x61, 0x6d, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0c, 0x67, 0x61, 0x6d, 0x65, 0x42, 0x61, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x47, 0x0a, 0x11, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x48, 0x44, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59,
	0x5f, 0x43, 0x32, 0x53, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x6a, 0x0a, 0x11, 0x47, 0x41, 0x4d, 0x45,
	0x5f, 0x48, 0x44, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x53, 0x32, 0x43, 0x12, 0x28, 0x0a,
	0x07, 0x6d, 0x61, 0x70, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07,
	0x6d, 0x61, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2b, 0x0a, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x22, 0x31, 0x0a, 0x19, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x48, 0x44, 0x5f,
	0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x4d, 0x41, 0x50, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x32,
	0x53, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x48, 0x0a, 0x19, 0x47, 0x41, 0x4d, 0x45, 0x5f,
	0x48, 0x44, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x4d, 0x41, 0x50, 0x4e, 0x4f, 0x44, 0x45,
	0x5f, 0x53, 0x32, 0x43, 0x12, 0x2b, 0x0a, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47,
	0x61, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x22, 0x1b, 0x0a, 0x19, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x48, 0x44, 0x5f, 0x47, 0x45, 0x54,
	0x4e, 0x45, 0x58, 0x54, 0x44, 0x41, 0x59, 0x4d, 0x41, 0x50, 0x5f, 0x43, 0x32, 0x53, 0x22, 0x72,
	0x0a, 0x19, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x48, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x4e, 0x45, 0x58,
	0x54, 0x44, 0x41, 0x59, 0x4d, 0x41, 0x50, 0x5f, 0x53, 0x32, 0x43, 0x12, 0x28, 0x0a, 0x07, 0x6d,
	0x61, 0x70, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x6d, 0x61,
	0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2b, 0x0a, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x47, 0x61, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x22, 0x67, 0x0a, 0x13, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x48, 0x44, 0x5f, 0x42, 0x55,
	0x59, 0x48, 0x45, 0x52, 0x4f, 0x5f, 0x43, 0x32, 0x53, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x65, 0x72,
	0x6f, 0x55, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x68, 0x65, 0x72, 0x6f,
	0x55, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x72, 0x65, 0x61, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x72, 0x65, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x75, 0x73, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x42, 0x0a, 0x13, 0x47,
	0x41, 0x4d, 0x45, 0x5f, 0x48, 0x44, 0x5f, 0x42, 0x55, 0x59, 0x48, 0x45, 0x52, 0x4f, 0x5f, 0x53,
	0x32, 0x43, 0x12, 0x2b, 0x0a, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x61, 0x6d,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x30, 0x0a, 0x14, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x48, 0x44, 0x5f, 0x53, 0x45, 0x4c, 0x4c, 0x48,
	0x45, 0x52, 0x4f, 0x5f, 0x43, 0x32, 0x53, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x65, 0x72, 0x6f, 0x55,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x68, 0x65, 0x72, 0x6f, 0x55, 0x69,
	0x64, 0x22, 0xe0, 0x02, 0x0a, 0x14, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x48, 0x44, 0x5f, 0x53, 0x45,
	0x4c, 0x4c, 0x48, 0x45, 0x52, 0x4f, 0x5f, 0x53, 0x32, 0x43, 0x12, 0x4b, 0x0a, 0x0a, 0x62, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x41, 0x72, 0x65, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x48, 0x44, 0x5f, 0x53,
	0x45, 0x4c, 0x4c, 0x48, 0x45, 0x52, 0x4f, 0x5f, 0x53, 0x32, 0x43, 0x2e, 0x42, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x41, 0x72, 0x65, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x62, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x41, 0x72, 0x65, 0x61, 0x12, 0x4e, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x70, 0x61,
	0x72, 0x65, 0x41, 0x72, 0x65, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x48, 0x44, 0x5f, 0x53, 0x45, 0x4c,
	0x4c, 0x48, 0x45, 0x52, 0x4f, 0x5f, 0x53, 0x32, 0x43, 0x2e, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72,
	0x65, 0x41, 0x72, 0x65, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x70,
	0x61, 0x72, 0x65, 0x41, 0x72, 0x65, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x6f, 0x6c, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x67, 0x6f, 0x6c, 0x64, 0x1a, 0x4a, 0x0a, 0x0f, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x72, 0x65, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x21, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x72, 0x6f, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x4b, 0x0a, 0x10, 0x50, 0x72, 0x65, 0x70, 0x61,
	0x72, 0x65, 0x41, 0x72, 0x65, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x21, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x72, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x68, 0x0a, 0x14, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x48, 0x44, 0x5f,
	0x4d, 0x4f, 0x56, 0x45, 0x48, 0x45, 0x52, 0x4f, 0x5f, 0x43, 0x32, 0x53, 0x12, 0x18, 0x0a, 0x07,
	0x68, 0x65, 0x72, 0x6f, 0x55, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x68,
	0x65, 0x72, 0x6f, 0x55, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x72, 0x65, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x72, 0x65, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x75, 0x73, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0xcc,
	0x02, 0x0a, 0x14, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x48, 0x44, 0x5f, 0x4d, 0x4f, 0x56, 0x45, 0x48,
	0x45, 0x52, 0x4f, 0x5f, 0x53, 0x32, 0x43, 0x12, 0x4b, 0x0a, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x41, 0x72, 0x65, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x48, 0x44, 0x5f, 0x4d, 0x4f, 0x56, 0x45,
	0x48, 0x45, 0x52, 0x4f, 0x5f, 0x53, 0x32, 0x43, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x41,
	0x72, 0x65, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x41, 0x72, 0x65, 0x61, 0x12, 0x4e, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x41,
	0x72, 0x65, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x48, 0x44, 0x5f, 0x4d, 0x4f, 0x56, 0x45, 0x48, 0x45,
	0x52, 0x4f, 0x5f, 0x53, 0x32, 0x43, 0x2e, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x41, 0x72,
	0x65, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65,
	0x41, 0x72, 0x65, 0x61, 0x1a, 0x4a, 0x0a, 0x0f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x72,
	0x65, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x48, 0x65, 0x72, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x4b, 0x0a, 0x10, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x41, 0x72, 0x65, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65,
	0x72, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x54, 0x0a,
	0x1d, 0x4c, 0x4f, 0x42, 0x42, 0x59, 0x5f, 0x4f, 0x4e, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x55,
	0x53, 0x45, 0x52, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x12, 0x33,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x22, 0x2e, 0x0a, 0x18, 0x4f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x42, 0x0a, 0x12, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x4f, 0x4e, 0x4b, 0x49,
	0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x62, 0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x62, 0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_msg_proto_rawDescOnce sync.Once
	file_msg_proto_rawDescData = file_msg_proto_rawDesc
)

func file_msg_proto_rawDescGZIP() []byte {
	file_msg_proto_rawDescOnce.Do(func() {
		file_msg_proto_rawDescData = protoimpl.X.CompressGZIP(file_msg_proto_rawDescData)
	})
	return file_msg_proto_rawDescData
}

var file_msg_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_msg_proto_goTypes = []interface{}{
	(*Vec2)(nil),                          // 0: proto.Vec2
	(*Int32ArrayInfo)(nil),                // 1: proto.Int32ArrayInfo
	(*UserInfo)(nil),                      // 2: proto.UserInfo
	(*Hero)(nil),                          // 3: proto.Hero
	(*TavernInfo)(nil),                    // 4: proto.TavernInfo
	(*EnemyInfo)(nil),                     // 5: proto.EnemyInfo
	(*EncounterInfo)(nil),                 // 6: proto.EncounterInfo
	(*PlayerInfo)(nil),                    // 7: proto.PlayerInfo
	(*GameData)(nil),                      // 8: proto.GameData
	(*MapNode)(nil),                       // 9: proto.MapNode
	(*MapLayer)(nil),                      // 10: proto.MapLayer
	(*MapData)(nil),                       // 11: proto.MapData
	(*S2C_RESULT)(nil),                    // 12: proto.S2C_RESULT
	(*LOGIN_HD_GUESTLOGIN_C2S)(nil),       // 13: proto.LOGIN_HD_GUESTLOGIN_C2S
	(*LOGIN_HD_GUESTLOGIN_S2C)(nil),       // 14: proto.LOGIN_HD_GUESTLOGIN_S2C
	(*LOBBY_HD_TRYLOGIN_C2S)(nil),         // 15: proto.LOBBY_HD_TRYLOGIN_C2S
	(*LOBBY_HD_TRYLOGIN_S2C)(nil),         // 16: proto.LOBBY_HD_TRYLOGIN_S2C
	(*GAME_HD_ENTRY_C2S)(nil),             // 17: proto.GAME_HD_ENTRY_C2S
	(*GAME_HD_ENTRY_S2C)(nil),             // 18: proto.GAME_HD_ENTRY_S2C
	(*GAME_HD_SELECTMAPNODE_C2S)(nil),     // 19: proto.GAME_HD_SELECTMAPNODE_C2S
	(*GAME_HD_SELECTMAPNODE_S2C)(nil),     // 20: proto.GAME_HD_SELECTMAPNODE_S2C
	(*GAME_HD_GETNEXTDAYMAP_C2S)(nil),     // 21: proto.GAME_HD_GETNEXTDAYMAP_C2S
	(*GAME_HD_GETNEXTDAYMAP_S2C)(nil),     // 22: proto.GAME_HD_GETNEXTDAYMAP_S2C
	(*GAME_HD_BUYHERO_C2S)(nil),           // 23: proto.GAME_HD_BUYHERO_C2S
	(*GAME_HD_BUYHERO_S2C)(nil),           // 24: proto.GAME_HD_BUYHERO_S2C
	(*GAME_HD_SELLHERO_C2S)(nil),          // 25: proto.GAME_HD_SELLHERO_C2S
	(*GAME_HD_SELLHERO_S2C)(nil),          // 26: proto.GAME_HD_SELLHERO_S2C
	(*GAME_HD_MOVEHERO_C2S)(nil),          // 27: proto.GAME_HD_MOVEHERO_C2S
	(*GAME_HD_MOVEHERO_S2C)(nil),          // 28: proto.GAME_HD_MOVEHERO_S2C
	(*LOBBY_ONUPDATEUSERINFO_NOTIFY)(nil), // 29: proto.LOBBY_ONUPDATEUSERINFO_NOTIFY
	(*OnUpdatePlayerInfoNotify)(nil),      // 30: proto.OnUpdatePlayerInfoNotify
	(*GAME_ONKICK_NOTIFY)(nil),            // 31: proto.GAME_ONKICK_NOTIFY
	nil,                                   // 32: proto.EnemyInfo.BattleAreaEntry
	nil,                                   // 33: proto.PlayerInfo.BattleAreaEntry
	nil,                                   // 34: proto.PlayerInfo.PrepareAreaEntry
	nil,                                   // 35: proto.GAME_HD_SELLHERO_S2C.BattleAreaEntry
	nil,                                   // 36: proto.GAME_HD_SELLHERO_S2C.PrepareAreaEntry
	nil,                                   // 37: proto.GAME_HD_MOVEHERO_S2C.BattleAreaEntry
	nil,                                   // 38: proto.GAME_HD_MOVEHERO_S2C.PrepareAreaEntry
}
var file_msg_proto_depIdxs = []int32{
	1,  // 0: proto.Hero.extraAttrs:type_name -> proto.Int32ArrayInfo
	3,  // 1: proto.TavernInfo.heros:type_name -> proto.Hero
	32, // 2: proto.EnemyInfo.battleArea:type_name -> proto.EnemyInfo.BattleAreaEntry
	7,  // 3: proto.EncounterInfo.data_1:type_name -> proto.PlayerInfo
	5,  // 4: proto.EncounterInfo.data_2:type_name -> proto.EnemyInfo
	4,  // 5: proto.EncounterInfo.data_3:type_name -> proto.TavernInfo
	33, // 6: proto.PlayerInfo.battleArea:type_name -> proto.PlayerInfo.BattleAreaEntry
	34, // 7: proto.PlayerInfo.prepareArea:type_name -> proto.PlayerInfo.PrepareAreaEntry
	7,  // 8: proto.GameData.player:type_name -> proto.PlayerInfo
	6,  // 9: proto.GameData.encounter:type_name -> proto.EncounterInfo
	9,  // 10: proto.MapLayer.nodes:type_name -> proto.MapNode
	10, // 11: proto.MapData.maps:type_name -> proto.MapLayer
	2,  // 12: proto.LOBBY_HD_TRYLOGIN_S2C.user:type_name -> proto.UserInfo
	7,  // 13: proto.LOBBY_HD_TRYLOGIN_S2C.gameBaseData:type_name -> proto.PlayerInfo
	11, // 14: proto.GAME_HD_ENTRY_S2C.mapData:type_name -> proto.MapData
	8,  // 15: proto.GAME_HD_ENTRY_S2C.gameData:type_name -> proto.GameData
	8,  // 16: proto.GAME_HD_SELECTMAPNODE_S2C.gameData:type_name -> proto.GameData
	11, // 17: proto.GAME_HD_GETNEXTDAYMAP_S2C.mapData:type_name -> proto.MapData
	8,  // 18: proto.GAME_HD_GETNEXTDAYMAP_S2C.gameData:type_name -> proto.GameData
	8,  // 19: proto.GAME_HD_BUYHERO_S2C.gameData:type_name -> proto.GameData
	35, // 20: proto.GAME_HD_SELLHERO_S2C.battleArea:type_name -> proto.GAME_HD_SELLHERO_S2C.BattleAreaEntry
	36, // 21: proto.GAME_HD_SELLHERO_S2C.prepareArea:type_name -> proto.GAME_HD_SELLHERO_S2C.PrepareAreaEntry
	37, // 22: proto.GAME_HD_MOVEHERO_S2C.battleArea:type_name -> proto.GAME_HD_MOVEHERO_S2C.BattleAreaEntry
	38, // 23: proto.GAME_HD_MOVEHERO_S2C.prepareArea:type_name -> proto.GAME_HD_MOVEHERO_S2C.PrepareAreaEntry
	30, // 24: proto.LOBBY_ONUPDATEUSERINFO_NOTIFY.list:type_name -> proto.OnUpdatePlayerInfoNotify
	3,  // 25: proto.EnemyInfo.BattleAreaEntry.value:type_name -> proto.Hero
	3,  // 26: proto.PlayerInfo.BattleAreaEntry.value:type_name -> proto.Hero
	3,  // 27: proto.PlayerInfo.PrepareAreaEntry.value:type_name -> proto.Hero
	3,  // 28: proto.GAME_HD_SELLHERO_S2C.BattleAreaEntry.value:type_name -> proto.Hero
	3,  // 29: proto.GAME_HD_SELLHERO_S2C.PrepareAreaEntry.value:type_name -> proto.Hero
	3,  // 30: proto.GAME_HD_MOVEHERO_S2C.BattleAreaEntry.value:type_name -> proto.Hero
	3,  // 31: proto.GAME_HD_MOVEHERO_S2C.PrepareAreaEntry.value:type_name -> proto.Hero
	32, // [32:32] is the sub-list for method output_type
	32, // [32:32] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_msg_proto_init() }
func file_msg_proto_init() {
	if File_msg_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_msg_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Vec2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Int32ArrayInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Hero); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TavernInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnemyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EncounterInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MapNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MapLayer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MapData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_RESULT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LOGIN_HD_GUESTLOGIN_C2S); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LOGIN_HD_GUESTLOGIN_S2C); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LOBBY_HD_TRYLOGIN_C2S); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LOBBY_HD_TRYLOGIN_S2C); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GAME_HD_ENTRY_C2S); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GAME_HD_ENTRY_S2C); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GAME_HD_SELECTMAPNODE_C2S); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GAME_HD_SELECTMAPNODE_S2C); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GAME_HD_GETNEXTDAYMAP_C2S); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GAME_HD_GETNEXTDAYMAP_S2C); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GAME_HD_BUYHERO_C2S); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GAME_HD_BUYHERO_S2C); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GAME_HD_SELLHERO_C2S); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GAME_HD_SELLHERO_S2C); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GAME_HD_MOVEHERO_C2S); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GAME_HD_MOVEHERO_S2C); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LOBBY_ONUPDATEUSERINFO_NOTIFY); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnUpdatePlayerInfoNotify); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GAME_ONKICK_NOTIFY); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_msg_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_msg_proto_goTypes,
		DependencyIndexes: file_msg_proto_depIdxs,
		MessageInfos:      file_msg_proto_msgTypes,
	}.Build()
	File_msg_proto = out.File
	file_msg_proto_rawDesc = nil
	file_msg_proto_goTypes = nil
	file_msg_proto_depIdxs = nil
}
