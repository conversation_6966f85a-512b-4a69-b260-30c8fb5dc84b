package battle

// 一个英雄
type IHero interface {
	GetUID() string
	GetExtraAttrs() [][]int32 //额外属性 {type, id, value} type: 0.基础属性 1.效果
	GetId() int32
	GetCost() int32
	GetQuality() int8
	GetAreaType() int8
	GetIndex() int8
}

type Areas map[int8]*IHero

type IPlayerInfo interface {
	GetUID() string        //uid
	GetBattleAreas() Areas //战斗区域
	GetHP() int32          //生命
	GetRoleId() int32      //角色id
}
